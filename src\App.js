import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import HospitalLogin from './pages/HospitalLogin';
import HospitalDashboard from './pages/HospitalDashboard';
import UserLogin from './pages/UserLogin';
import UserDashboard from './pages/UserDashboard';
import './App.css';

function App() {
  const [user, setUser] = useState(null);
  const [userType, setUserType] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Check for stored authentication
    const storedUser = localStorage.getItem('bloodBankUser');
    const storedUserType = localStorage.getItem('bloodBankUserType');
    
    if (storedUser && storedUserType) {
      setUser(JSON.parse(storedUser));
      setUserType(storedUserType);
    }
  }, []);

  const handleLogin = (userData, type) => {
    setUser(userData);
    setUserType(type);
    localStorage.setItem('bloodBankUser', JSON.stringify(userData));
    localStorage.setItem('bloodBankUserType', type);
    
    if (type === 'hospital') {
      navigate('/hospital/dashboard');
    } else {
      navigate('/user/dashboard');
    }
  };

  const handleLogout = () => {
    setUser(null);
    setUserType(null);
    localStorage.removeItem('bloodBankUser');
    localStorage.removeItem('bloodBankUserType');
    navigate('/');
  };

  return (
    <div className="App">
      {/* Navigation Bar */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-danger">
        <div className="container">
          <a className="navbar-brand" href="/">
            <i className="fas fa-tint me-2"></i>
            Blood Bank Management System
          </a>
          
          {user && (
            <div className="navbar-nav ms-auto">
              <span className="navbar-text me-3">
                Welcome, {user.name || user.hospitalName}
              </span>
              <button className="btn btn-outline-light btn-sm" onClick={handleLogout}>
                <i className="fas fa-sign-out-alt me-1"></i>
                Logout
              </button>
            </div>
          )}
        </div>
      </nav>

      {/* Main Content */}
      <div className="container-fluid">
        <Routes>
          {/* Home Route */}
          <Route path="/" element={<HomePage />} />
          
          {/* Hospital Routes */}
          <Route 
            path="/hospital/login" 
            element={
              user && userType === 'hospital' ? 
              <Navigate to="/hospital/dashboard" /> : 
              <HospitalLogin onLogin={handleLogin} />
            } 
          />
          <Route 
            path="/hospital/dashboard" 
            element={
              user && userType === 'hospital' ? 
              <HospitalDashboard user={user} /> : 
              <Navigate to="/hospital/login" />
            } 
          />
          
          {/* User Routes */}
          <Route 
            path="/user/login" 
            element={
              user && userType === 'user' ? 
              <Navigate to="/user/dashboard" /> : 
              <UserLogin onLogin={handleLogin} />
            } 
          />
          <Route 
            path="/user/dashboard" 
            element={
              user && userType === 'user' ? 
              <UserDashboard user={user} /> : 
              <Navigate to="/user/login" />
            } 
          />
          
          {/* Default redirect */}
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </div>
    </div>
  );
}

// Home Page Component
function HomePage() {
  const navigate = useNavigate();

  return (
    <div className="home-page">
      {/* Hero Section */}
      <div className="hero-section bg-danger text-white py-5">
        <div className="container text-center">
          <h1 className="display-4 mb-4">
            <i className="fas fa-tint me-3"></i>
            Blood Bank Management System
          </h1>
          <p className="lead mb-4">
            Connecting hospitals and donors to save lives through efficient blood management
          </p>
          <div className="row justify-content-center">
            <div className="col-md-8">
              <div className="row">
                <div className="col-md-6 mb-3">
                  <div className="card dashboard-card h-100">
                    <div className="card-body text-center text-dark">
                      <i className="fas fa-hospital fa-3x text-danger mb-3"></i>
                      <h5>Hospital Portal</h5>
                      <p>Manage blood inventory and handle donation requests</p>
                      <button 
                        className="btn btn-blood"
                        onClick={() => navigate('/hospital/login')}
                      >
                        Hospital Login
                      </button>
                    </div>
                  </div>
                </div>
                <div className="col-md-6 mb-3">
                  <div className="card dashboard-card h-100">
                    <div className="card-body text-center text-dark">
                      <i className="fas fa-user fa-3x text-danger mb-3"></i>
                      <h5>User Portal</h5>
                      <p>Find nearby hospitals and request blood donations</p>
                      <button 
                        className="btn btn-blood"
                        onClick={() => navigate('/user/login')}
                      >
                        User Login
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="features-section py-5">
        <div className="container">
          <h2 className="text-center mb-5">Key Features</h2>
          <div className="row">
            <div className="col-md-4 mb-4">
              <div className="card dashboard-card h-100">
                <div className="card-body text-center">
                  <i className="fas fa-search-location fa-3x text-danger mb-3"></i>
                  <h5>Hospital Search</h5>
                  <p>Find nearby hospitals with available blood types using location-based search</p>
                </div>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="card dashboard-card h-100">
                <div className="card-body text-center">
                  <i className="fas fa-chart-bar fa-3x text-danger mb-3"></i>
                  <h5>Inventory Management</h5>
                  <p>Real-time tracking of blood inventory with automated alerts for low stock</p>
                </div>
              </div>
            </div>
            <div className="col-md-4 mb-4">
              <div className="card dashboard-card h-100">
                <div className="card-body text-center">
                  <i className="fas fa-handshake fa-3x text-danger mb-3"></i>
                  <h5>Request Management</h5>
                  <p>Streamlined process for blood requests and donations with status tracking</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Blood Types Info */}
      <div className="blood-types-section py-5 bg-light">
        <div className="container">
          <h2 className="text-center mb-5">Blood Type Compatibility</h2>
          <div className="row justify-content-center">
            <div className="col-md-8">
              <div className="text-center">
                <div className="mb-3">
                  <span className="blood-type-badge blood-type-A">A+</span>
                  <span className="blood-type-badge blood-type-A">A-</span>
                  <span className="blood-type-badge blood-type-B">B+</span>
                  <span className="blood-type-badge blood-type-B">B-</span>
                </div>
                <div className="mb-3">
                  <span className="blood-type-badge blood-type-AB">AB+</span>
                  <span className="blood-type-badge blood-type-AB">AB-</span>
                  <span className="blood-type-badge blood-type-O">O+</span>
                  <span className="blood-type-badge blood-type-O">O-</span>
                </div>
                <p className="text-muted">
                  Our system manages all blood types and ensures compatibility matching for safe transfusions
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
