import React, { useState } from 'react';

function BloodInventory({ inventory, onUpdateCount }) {
  const [editMode, setEditMode] = useState({});
  const [newCounts, setNewCounts] = useState({});

  const startEdit = (bloodType) => {
    setEditMode({ ...editMode, [bloodType]: true });
    setNewCounts({ ...newCounts, [bloodType]: inventory[bloodType] });
  };

  const saveEdit = (bloodType) => {
    onUpdateCount(bloodType, newCounts[bloodType]);
    setEditMode({ ...editMode, [bloodType]: false });
  };

  return (
    <div className="blood-inventory">
      <h3>Blood Inventory</h3>
      <table>
        <thead>
          <tr>
            <th>Blood Type</th>
            <th>Available Units</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {Object.entries(inventory).map(([bloodType, count]) => (
            <tr key={bloodType}>
              <td>{bloodType}</td>
              <td>
                {editMode[bloodType] ? (
                  <input 
                    type="number" 
                    value={newCounts[bloodType]} 
                    onChange={(e) => setNewCounts({...newCounts, [bloodType]: e.target.value})}
                    min="0"
                  />
                ) : (
                  count
                )}
              </td>
              <td>
                {editMode[bloodType] ? (
                  <button onClick={() => saveEdit(bloodType)}>Save</button>
                ) : (
                  <button onClick={() => startEdit(bloodType)}>Update</button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default BloodInventory;