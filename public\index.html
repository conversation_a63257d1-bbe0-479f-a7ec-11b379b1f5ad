<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Blood Bank Management System - Connecting hospitals and users for efficient blood donation and distribution" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <title>Blood Bank Management System</title>
    
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f8f9fa;
      }
      
      .navbar-brand {
        font-weight: bold;
        color: #dc3545 !important;
      }
      
      .blood-type-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        margin: 0.25rem;
        border-radius: 25px;
        font-weight: bold;
        color: white;
      }
      
      .blood-type-A { background-color: #dc3545; }
      .blood-type-B { background-color: #28a745; }
      .blood-type-AB { background-color: #007bff; }
      .blood-type-O { background-color: #ffc107; color: #000; }
      
      .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
      }
      
      .dashboard-card:hover {
        transform: translateY(-2px);
      }
      
      .status-pending { color: #ffc107; }
      .status-approved { color: #28a745; }
      .status-rejected { color: #dc3545; }
      
      .inventory-table th {
        background-color: #dc3545;
        color: white;
        border: none;
      }
      
      .btn-blood {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
      }
      
      .btn-blood:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
