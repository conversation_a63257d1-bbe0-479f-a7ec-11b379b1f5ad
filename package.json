{"name": "blood-bank-management-system", "version": "1.0.0", "description": "A comprehensive blood bank management system for hospitals and users", "main": "src/index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server/server.js", "dev": "concurrently \"npm run server\" \"npm start\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "axios": "^1.3.0", "express": "^4.18.2", "cors": "^2.8.5", "mongoose": "^6.9.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "dotenv": "^16.0.3"}, "devDependencies": {"concurrently": "^7.6.0", "nodemon": "^2.0.20"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}