.App {
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Dashboard Cards */
.dashboard-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* Blood Type Badges */
.blood-type-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0.25rem;
  border-radius: 25px;
  font-weight: bold;
  color: white;
  font-size: 0.9rem;
}

.blood-type-A { background-color: #dc3545; }
.blood-type-B { background-color: #28a745; }
.blood-type-AB { background-color: #007bff; }
.blood-type-O { background-color: #ffc107; color: #000; }

/* Status Colors */
.status-pending { color: #ffc107; }
.status-approved { color: #28a745; }
.status-rejected { color: #dc3545; }

/* Custom Buttons */
.btn-blood {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
  border-radius: 25px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-blood:hover {
  background-color: #c82333;
  border-color: #bd2130;
  color: white;
  transform: translateY(-1px);
}

/* Tables */
.inventory-table th {
  background-color: #dc3545;
  color: white;
  border: none;
  font-weight: 600;
}

.inventory-table td {
  vertical-align: middle;
  border-color: #dee2e6;
}

/* Forms */
.form-control:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Login Pages */
.login-container {
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  max-width: 400px;
  width: 100%;
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.login-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  padding: 2rem;
  text-align: center;
}

/* Dashboard Layout */
.dashboard-container {
  padding: 2rem 0;
}

.dashboard-header {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-content {
  background: white;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Tabs */
.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
  padding: 1rem 1.5rem;
  border-radius: 10px 10px 0 0;
}

.nav-tabs .nav-link.active {
  background-color: #dc3545;
  color: white;
  border: none;
}

.nav-tabs .nav-link:hover {
  border: none;
  color: #dc3545;
}

/* Request Cards */
.request-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.request-header {
  background-color: #f8f9fa;
  border-radius: 10px 10px 0 0;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

/* Hospital Search */
.hospital-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.hospital-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hospital-header {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border-radius: 10px 10px 0 0;
  padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 0;
  }
  
  .hero-section h1 {
    font-size: 2rem;
  }
  
  .dashboard-container {
    padding: 1rem 0;
  }
  
  .dashboard-header,
  .dashboard-content {
    margin: 0 1rem;
    padding: 1rem;
  }
  
  .blood-type-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Alert Styles */
.alert-custom {
  border: none;
  border-radius: 10px;
  padding: 1rem 1.5rem;
}

.alert-success-custom {
  background-color: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.alert-danger-custom {
  background-color: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.alert-warning-custom {
  background-color: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
